CREATE TABLE "device_schedule_job_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"imei" text NOT NULL,
	"action" "immobilizer_action" NOT NULL,
	"task_id" text,
	"api_response" text,
	"success" text DEFAULT 'true' NOT NULL,
	"error_message" text,
	"executed_at" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "devices" RENAME COLUMN "immobilizer_status" TO "user_status";--> statement-breakpoint
ALTER TABLE "devices" ADD COLUMN "system_status" text DEFAULT 'engineResume' NOT NULL;