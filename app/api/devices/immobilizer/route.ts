import { NextRequest, NextResponse } from 'next/server';
import { getDeviceByImei, updateDeviceUserStatus, createUserImmobilizerLog, createScheduleJobLog, getAdminConfig, updateScheduleJobLogTaskData } from '@/lib/db/queries';
import { db, deviceScheduleJobLogs } from '@/lib/db';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { device_imei, action, username, task_id, task_data } = body;

    if (!device_imei || !action || !username) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device IMEI, action, and username are required',
        },
        { status: 400 }
      );
    }

    // Validate username is not empty
    if (username.trim() === '') {
      return NextResponse.json(
        {
          success: false,
          error: 'Username cannot be empty',
        },
        { status: 400 }
      );
    }

    if (!['on', 'off'].includes(action.toLowerCase())) {
      return NextResponse.json(
        {
          success: false,
          error: 'Action must be either "on" or "off"',
        },
        { status: 400 }
      );
    }

    // Get current device status
    const device = await getDeviceByImei(device_imei);
    if (!device) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device not found',
        },
        { status: 404 }
      );
    }

    // Determine the new status based on action
    const newStatus = action.toLowerCase() === 'on' ? 'engineStop' : 'engineResume';
    const previousStatus = device.userStatus;

    // Update device user status
    const updatedDevice = await updateDeviceUserStatus(device_imei, newStatus);

    // Create log entry
    console.log('Creating log entry with:', {
      device_imei,
      vehicleName: device.name,
      username,
      previousStatus,
      newStatus,
      task_id,
      task_data
    });

    await createUserImmobilizerLog(
      device_imei,
      device.name,
      username,
      previousStatus as 'engineStop' | 'engineResume',
      newStatus,
      task_id,
      task_data
    );

    // Execute schedule job (call immobilizer API) after user changes status
    try {
      await executeUserTriggeredScheduleJob(device_imei, newStatus);
    } catch (scheduleError) {
      console.error('Schedule job execution failed:', scheduleError);
      // Don't fail the main request if schedule job fails
    }

    return NextResponse.json({
      success: true,
      data: {
        device: updatedDevice,
        previousStatus,
        newStatus,
        action: action.toLowerCase(),
      },
      message: `Immobilizer ${action.toLowerCase() === 'on' ? 'activated' : 'deactivated'} successfully`,
    });

  } catch (error) {
    console.error('Error updating immobilizer status:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update immobilizer status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper function to execute schedule job when user triggers immobilizer
async function executeUserTriggeredScheduleJob(imei: string, action: 'engineStop' | 'engineResume') {
  try {
    // Get admin credentials for authentication
    const adminConfig = await getAdminConfig();

    if (!adminConfig) {
      const errorMessage = 'Admin configuration not found. Cannot execute immobilizer command.';

      // Log the failed attempt
      await createScheduleJobLog({
        imei,
        action,
        trigger: 'user',
        success: 'false',
        errorMessage,
        executedAt: new Date(),
      });

      throw new Error(errorMessage);
    }

    // Authenticate with Track360 API
    const loginResponse = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/login',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: adminConfig.username,
          password: adminConfig.password,
        }),
        cache: 'no-store',
      }
    );

    if (!loginResponse.ok) {
      const loginErrorText = await loginResponse.text();
      const errorMessage = `Authentication failed with status: ${loginResponse.status}. Response: ${loginErrorText}`;

      // Log the failed attempt
      await createScheduleJobLog({
        imei,
        action,
        trigger: 'user',
        success: 'false',
        errorMessage,
        executedAt: new Date(),
      });

      throw new Error(errorMessage);
    }

    const loginData = await loginResponse.json();

    if (!loginData.token) {
      const errorMessage = `No token received from authentication. Login response: ${JSON.stringify(loginData)}`;

      // Log the failed attempt
      await createScheduleJobLog({
        imei,
        action,
        trigger: 'user',
        success: 'false',
        errorMessage,
        executedAt: new Date(),
      });

      throw new Error(errorMessage);
    }

    // Make the immobilizer API call
    let immobilizerResponse = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/set_owl_mode_v1',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${loginData.token}`,
        },
        body: JSON.stringify({
          device_imei: imei,
          type: action,
        }),
        cache: 'no-store',
      }
    );

    // If Bearer format fails with 422, try without Bearer prefix
    if (!immobilizerResponse.ok && immobilizerResponse.status === 422) {
      immobilizerResponse = await fetch(
        'https://prod-s2.track360.net.in/api/v1/auth/set_owl_mode_v1',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': loginData.token,
          },
          body: JSON.stringify({
            device_imei: imei,
            type: action,
          }),
          cache: 'no-store',
        }
      );
    }

    const responseText = await immobilizerResponse.text();
    let responseData;

    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      responseData = { raw_response: responseText };
    }

    if (!immobilizerResponse.ok) {
      const errorMessage = `Immobilizer API failed with status: ${immobilizerResponse.status}. Response: ${JSON.stringify(responseData)}`;

      // Log the failed attempt
      await createScheduleJobLog({
        imei,
        action,
        trigger: 'user',
        success: 'false',
        errorMessage,
        apiResponse: JSON.stringify(responseData),
        executedAt: new Date(),
      });

      throw new Error(errorMessage);
    }

    // Extract task_id from response
    const taskId = responseData.task_id;

    // Log the pending attempt (will be updated after task status check)
    const logEntry = await createScheduleJobLog({
      imei,
      action,
      trigger: 'user',
      taskId: taskId,
      apiResponse: JSON.stringify(responseData),
      success: 'pending',
      executedAt: new Date(),
    });

    // Check task status after a delay (similar to schedule API)
    if (taskId && logEntry) {
      setTimeout(async () => {
        try {
          const taskStatusResponse = await fetch(
            `https://prod-s2.track360.net.in/api/v1/auth/get_task_status?task_id=${taskId}`,
            {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${loginData.token}`,
              },
              cache: 'no-store',
            }
          );

          if (taskStatusResponse.ok) {
            const taskStatusData = await taskStatusResponse.json();

            // Determine if the task was actually successful based on the response
            let taskSuccess = 'true';
            let taskErrorMessage = null;

            if (taskStatusData.data) {
              // Check if status is "failed" in the data
              if (taskStatusData.data.status === 'failed') {
                taskSuccess = 'false';
                taskErrorMessage = taskStatusData.data.message || 'Task failed';
              }
            }

            // Update the log entry with task data
            await updateScheduleJobLogTaskData(logEntry.id, taskStatusData);

            // Update the success status based on task result
            console.log('Updating user-triggered job log status:', {
              logId: logEntry.id,
              taskSuccess,
              taskErrorMessage,
              taskStatus: taskStatusData.data?.status
            });

            const updateResult = await db
              .update(deviceScheduleJobLogs)
              .set({
                success: taskSuccess,
                errorMessage: taskErrorMessage,
                updatedAt: new Date()
              })
              .where(eq(deviceScheduleJobLogs.id, logEntry.id))
              .returning();

            console.log('User-triggered job log updated:', updateResult[0]);
          } else {
            const errorText = await taskStatusResponse.text();
            console.error('Task status API failed for user-triggered job:', errorText);

            // Update log to indicate task status check failed
            await db
              .update(deviceScheduleJobLogs)
              .set({
                success: 'false',
                errorMessage: `Task status check failed: ${errorText}`,
                updatedAt: new Date()
              })
              .where(eq(deviceScheduleJobLogs.id, logEntry.id));
          }
        } catch (taskStatusError) {
          console.error('Error checking task status for user-triggered job:', taskStatusError);

          // Update log to indicate task status check error
          try {
            await db
              .update(deviceScheduleJobLogs)
              .set({
                success: 'false',
                errorMessage: `Task status check error: ${taskStatusError instanceof Error ? taskStatusError.message : 'Unknown error'}`,
                updatedAt: new Date()
              })
              .where(eq(deviceScheduleJobLogs.id, logEntry.id));
          } catch (updateError) {
            console.error('Failed to update log with task status error:', updateError);
          }
        }
      }, 5000); // 5 second delay
    }

    console.log('User-triggered schedule job executed successfully:', {
      imei,
      action,
      taskId,
      trigger: 'user'
    });

  } catch (error) {
    console.error('Error in user-triggered schedule job:', error);
    throw error;
  }
}
