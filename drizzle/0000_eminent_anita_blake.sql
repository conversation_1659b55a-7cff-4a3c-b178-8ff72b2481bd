CREATE TYPE "public"."immobilizer_action" AS ENUM('engineStop', 'engineResume');--> statement-breakpoint
CREATE TABLE "device_immobilizer_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"imei" text NOT NULL,
	"action" "immobilizer_action" NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "devices" (
	"id" serial PRIMARY KEY NOT NULL,
	"imei" text NOT NULL,
	"immobilizer_status" text DEFAULT 'engineResume' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "devices_imei_unique" UNIQUE("imei")
);
