import { NextRequest, NextResponse } from 'next/server';
import { getAdminConfig, upsertAdminConfig, syncDeviceFromVehicles, getAllDevices, softDeleteDevice } from '@/lib/db/queries';

export async function GET() {
  try {
    const admin = await getAdminConfig();
    
    if (!admin) {
      return NextResponse.json({
        success: true,
        data: null,
        message: 'No admin configuration found'
      });
    }

    // Return admin data including password for config purposes
    return NextResponse.json({
      success: true,
      data: admin,
    });

  } catch (error) {
    console.error('Error fetching admin config:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch admin configuration',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password } = body;

    if (!username || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Username and password are required',
        },
        { status: 400 }
      );
    }

    // Validate username (no spaces, minimum length)
    if (username.trim().length < 3) {
      return NextResponse.json(
        {
          success: false,
          error: 'Username must be at least 3 characters long',
        },
        { status: 400 }
      );
    }

    // Upsert admin configuration (save password as plain text for config)
    const admin = await upsertAdminConfig(username.trim(), password);

    // Auto sync devices after saving admin config
    let syncMessage = '';
    try {
      // Get current devices from database before sync
      const currentDevices = await getAllDevices();
      const currentImeis = new Set(currentDevices.map(device => device.imei));

      // Fetch vehicles from Track360 API using pull_api_v2
      const credentials = Buffer.from(`${username.trim()}:${password}`).toString('base64');
      const vehiclesResponse = await fetch(
        'https://prod-s2.track360.net.in/api/v1/auth/pull_api_v2?bms=true',
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${credentials}`,
          },
          cache: 'no-store',
        }
      );

      if (vehiclesResponse.ok) {
        const vehiclesData = await vehiclesResponse.json();

        if (vehiclesData.data && Array.isArray(vehiclesData.data)) {
          const syncResults = [];
          const apiImeis = new Set();

          // Sync all devices from API
          for (const vehicle of vehiclesData.data) {
            if (vehicle.deviceImei) {
              apiImeis.add(vehicle.deviceImei);
              try {
                await syncDeviceFromVehicles(vehicle.deviceImei, vehicle);
                syncResults.push(vehicle.deviceImei);
              } catch (error) {
                console.error(`Error syncing device ${vehicle.deviceImei}:`, error);
              }
            }
          }

          // Soft delete devices that are not in API response
          const devicesToDelete = [];
          for (const imei of currentImeis) {
            if (!apiImeis.has(imei)) {
              try {
                await softDeleteDevice(imei);
                devicesToDelete.push(imei);
              } catch (error) {
                console.error(`Error soft deleting device ${imei}:`, error);
              }
            }
          }

          syncMessage = ` Auto sync completed: ${syncResults.length} devices synced, ${devicesToDelete.length} devices soft deleted.`;
        }
      }
    } catch (error) {
      console.error('Auto sync failed:', error);
      syncMessage = ' Auto sync failed, but admin config was saved.';
    }

    return NextResponse.json({
      success: true,
      data: admin,
      message: `Admin configuration saved successfully.${syncMessage}`,
    });

  } catch (error) {
    console.error('Error saving admin config:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to save admin configuration',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
