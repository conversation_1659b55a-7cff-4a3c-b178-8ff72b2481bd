'use client';

import { useRouter } from 'next/navigation';
import { LogOut } from 'lucide-react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';

export default function VehicleSelectHeader() {
  const router = useRouter();
  const { logout } = useAuth();

  const handleLogout = () => {
    logout();
    router.push('/user/login');
  };

  return (
    <div className="shadow-sm" style={{ backgroundColor: '#24CFFA' }}>
      <div className="flex items-center justify-between px-4 py-4">
        <button
          onClick={handleLogout}
          className="w-10 h-10 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors"
          title="Logout"
        >
          <LogOut className="h-5 w-5" />
        </button>
        <Image
          src="/maka_logo.png"
          alt="MAKA Motors"
          width={50}
          height={16}
          className="h-4 w-auto"
        />
      </div>
    </div>
  );
}
