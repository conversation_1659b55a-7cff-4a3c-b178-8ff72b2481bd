'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Lock, Loader2, Wifi, WifiOff } from 'lucide-react';
import Image from 'next/image';
import { VehicleDetail, ApiResponse } from '@/types/vehicle';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import VehicleInfoHeader from '@/components/headers/VehicleInfoHeader';

function VehicleInfoContent() {
  const [isImmobilized, setIsImmobilized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [vehicleDetail, setVehicleDetail] = useState<VehicleDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();
  const { token: contextToken, credentials, logout } = useAuth();

  // Fetch vehicle details from API using IMEI for stable filtering
  useEffect(() => {
    const fetchVehicleDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get vehicle IMEI from URL parameter
        const vehicleImei = searchParams.get('imei');
        if (!vehicleImei) {
          router.push('/user/vehicle-select');
          return;
        }

        // Get credentials from auth context
        if (!credentials) {
          throw new Error('Authentication credentials not available');
        }

        // Fetch detailed vehicle information using IMEI for more stable filtering
        const response = await fetch(`/api/vehicles/imei/${encodeURIComponent(vehicleImei)}`, {
          headers: {
            'x-username': credentials.username,
            'x-password': credentials.password,
          },
        });
        const data: ApiResponse<VehicleDetail> = await response.json();

        if (!response.ok) {
          throw new Error(data.message || 'Failed to fetch vehicle details');
        }

        if (data.success && data.data) {
          setVehicleDetail(data.data);

          // Fetch immobilizer status from database (more reliable than API)
          try {
            const statusResponse = await fetch(`/api/vehicles/immobilizer/status/${encodeURIComponent(vehicleImei)}`);
            const statusData = await statusResponse.json();
            if (statusData.success) {
              setIsImmobilized(statusData.data.isImmobilized);
            } else {
              // Fallback to API owl_mode status if database not available
              setIsImmobilized(data.data.armed); // This comes from owl_mode in API transformation
            }
          } catch (statusError) {
            console.error('Error fetching immobilizer status:', statusError);
            // Fallback to API owl_mode status
            setIsImmobilized(data.data.armed); // This comes from owl_mode in API transformation
          }
        } else {
          throw new Error(data.error || 'Failed to load vehicle details');
        }
      } catch (err) {
        console.error('Error fetching vehicle details:', err);
        setError(err instanceof Error ? err.message : 'Failed to load vehicle details');
      } finally {
        setLoading(false);
      }
    };

    fetchVehicleDetails();
  }, [router, searchParams, credentials]);



  const handleImmobilizationToggle = async () => {
    if (!vehicleDetail) return;

    setIsLoading(true);

    try {
      // Toggle immobilizer (only log, no external API call)
      const action = isImmobilized ? 'off' : 'on';
      const response = await fetch('/api/devices/immobilizer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device_imei: vehicleDetail.deviceImei,
          action: action,
          username: credentials?.username || 'user', // Get username from auth context
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Failed to control immobilizer');
      }

      // Update state on success and refresh status from database
      setIsImmobilized(!isImmobilized);

      // Refresh status from database to ensure accuracy
      try {
        const statusResponse = await fetch(`/api/vehicles/immobilizer/status/${encodeURIComponent(vehicleDetail.deviceImei)}`);
        const statusData = await statusResponse.json();
        if (statusData.success) {
          setIsImmobilized(statusData.data.isImmobilized);
        }
      } catch (refreshError) {
        console.error('Error refreshing status:', refreshError);
        // Continue with local state update
      }

    } catch (error) {
      console.error('Immobilizer toggle error:', error);
      alert(`Failed to ${isImmobilized ? 'deactivate' : 'activate'} immobilizer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };



  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading vehicle details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center px-6">
          <p className="text-red-600 mb-4">{error}</p>
          <Button
            onClick={() => router.push('/user/vehicle-select')}
            variant="outline"
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
          >
            Back to Vehicle Selection
          </Button>
        </div>
      </div>
    );
  }

  if (!vehicleDetail) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center px-6">
          <p className="text-gray-600 mb-4">No vehicle data available</p>
          <Button
            onClick={() => router.push('/user/vehicle-select')}
            variant="outline"
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
          >
            Back to Vehicle Selection
          </Button>
        </div>
      </div>
    );
  }

  // Get motor image based on name from API
  const getMotorImage = () => {
    const vehicleName = vehicleDetail.name?.toLowerCase() || '';

    // Check if vehicle name contains color keywords from API
    if (vehicleName.includes('red')) {
      return '/maka_full_red.png';
    } else if (vehicleName.includes('white')) {
      return '/maka_full_white.png';
    } else if (vehicleName.includes('beige')) {
      return '/maka_full_beige.png';
    }

    // Default to beige if no color found in name
    return '/maka_full_beige.png';
  };

  // Check if vehicle is online (you can adjust this logic based on your data)
  const isVehicleOnline = () => {
    // Check if last update was within last 5 minutes (300000 ms)
    if (!vehicleDetail.lastUpdate) return false;
    const lastUpdateTime = new Date(vehicleDetail.lastUpdate).getTime();
    const currentTime = new Date().getTime();
    const timeDifference = currentTime - lastUpdateTime;
    return timeDifference < 300000; // 5 minutes
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Mobile Header */}
      <VehicleInfoHeader />

      {/* Content */}
      <div className="flex-1 flex flex-col bg-white">
        {/* Top Section with License Plate and Greeting */}
        <div className="px-6 pt-6 pb-4">
          <div className="flex justify-between items-start">
            {/* License Plate */}
            <div>
              <p className="text-sm text-gray-600 mb-1">Licence Plate</p>
              <p className="text-2xl font-bold text-gray-900">
                {vehicleDetail.plate || 'N/A'}
              </p>
            </div>

            {/* Greeting */}
            <div className="text-right">
              <p className="text-lg text-gray-700">

              </p>
            </div>
          </div>
        </div>

        {/* VIN and Model */}
        <div className="px-6 pb-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-600">VIN</p>
              <p className="text-sm font-medium text-gray-900">
                {vehicleDetail.deviceImei || 'N/A'}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">MODEL</p>
              <p className="text-sm font-bold text-gray-900" style={{ fontFamily: 'serif' }}>
                Cavalry
              </p>
            </div>
          </div>
        </div>

        {/* Vehicle Image with Background Text */}
        <div className="relative px-6 pb-6">
          {/* Background Text */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <p className="text-8xl font-bold text-gray-200 opacity-30 select-none">
              Victory Red
            </p>
          </div>

          {/* Vehicle Image */}
          <div className="relative z-10 flex justify-center">
            <div className="relative w-full max-w-sm">
              <Image
                src={getMotorImage()}
                alt="MAKA Motorcycle"
                width={400}
                height={300}
                className="w-full h-auto object-contain"
                priority
              />
            </div>
          </div>
        </div>

        {/* Vehicle Name */}
        <div className="px-6 pb-6 text-center">
          <h2 className="text-xl font-bold text-gray-900">
            {vehicleDetail.name || 'MAKA Cavalry RnD Victory Red'}
          </h2>
        </div>

        {/* Stats Section */}
        <div className="px-6 pb-6">
          <div className="bg-white rounded-xl p-4 border border-gray-200">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-xs text-gray-600 mb-1">Level Baterai</p>
                <p className="text-lg font-bold text-gray-900">
                  {vehicleDetail.battery?.toFixed(1) || '0.0'}%
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-600 mb-1">Kecepatan</p>
                <p className="text-lg font-bold text-gray-900">
                  {vehicleDetail.speed || '0'} <span className="text-sm">km/h</span>
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-600 mb-1">Total Jarak</p>
                <p className="text-lg font-bold text-gray-900">
                  {((vehicleDetail.distance || 0) / 1000).toFixed(2)} <span className="text-sm">Km</span>
                </p>
              </div>
            </div>
          </div>
        </div>



        {/* Bottom Controls */}
        <div className="mt-auto bg-gray-100 mx-6 mb-8 rounded-xl p-4">
          <div className="flex items-center justify-between">
            {/* WiFi Status */}
            <div className="flex items-center gap-3">
              {isVehicleOnline() ? (
                <Wifi className="h-6 w-6 text-gray-700" />
              ) : (
                <WifiOff className="h-6 w-6 text-gray-500" />
              )}
              <div>
                <p className="text-sm font-medium text-gray-900">
                  IMEI {vehicleDetail.deviceImei || '35024206696243'}
                </p>
                <p className="text-xs text-gray-600">Ignition On</p>
                <p className={`text-xs font-medium ${isVehicleOnline() ? 'text-gray-900' : 'text-gray-500'}`}>
                  Status {isVehicleOnline() ? 'online' : 'offline'}
                </p>
              </div>
            </div>

            {/* Immobilization Toggle */}
            <div className="flex items-center gap-2">
              <div className="flex flex-col items-center">
                <div className={`relative inline-flex h-8 w-14 items-center rounded-full transition-colors ${
                  isImmobilized ? 'bg-red-500' : 'bg-cyan-400'
                } ${isLoading ? 'opacity-70' : ''}`}>
                  <div className={`inline-flex h-6 w-6 items-center justify-center rounded-full bg-white shadow-lg transition-transform ${
                    isImmobilized ? 'translate-x-7' : 'translate-x-1'
                  }`}>
                    {isLoading ? (
                      <div className="animate-spin rounded-full h-3 w-3 border border-gray-300 border-t-blue-600"></div>
                    ) : (
                      <Lock className={`h-3 w-3 ${isImmobilized ? 'text-red-500' : 'text-cyan-400'}`} />
                    )}
                  </div>
                  <button
                    onClick={handleImmobilizationToggle}
                    disabled={isLoading}
                    className="absolute inset-0 rounded-full disabled:cursor-not-allowed"
                    aria-label="Toggle immobilization"
                  />
                </div>
                <p className="text-xs font-medium text-gray-900 mt-1">
                  Immobilization {isImmobilized ? 'On' : 'Off'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Logout Button */}
        <div className="px-6 pb-8">
          <Button
            onClick={() => {
              logout();
              router.push('/user/login');
            }}
            variant="outline"
            className="w-full h-12 text-red-600 border-red-600 hover:bg-red-50 font-medium"
          >
            Logout
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function VehicleInfoPage() {
  return (
    <ProtectedRoute>
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center bg-white">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      }>
        <VehicleInfoContent />
      </Suspense>
    </ProtectedRoute>
  );
}
