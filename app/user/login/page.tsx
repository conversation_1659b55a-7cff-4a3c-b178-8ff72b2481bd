'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, User, Lock, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';

export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { login, isAuthenticated, loading } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (!loading && isAuthenticated) {
      router.push('/user/vehicle-select');
    }
  }, [isAuthenticated, loading, router]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const result = await login(username, password);

      if (result.success) {
        router.push('/user/vehicle-select');
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading while checking auth
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-cyan-400">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-cyan-400 to-cyan-500 flex flex-col">
      {/* Top section with logo and title */}
      <div className="flex-1 flex flex-col items-center justify-center px-6 pt-16 pb-8">
        {/* Logo */}
        <div className="mb-8">
          <Image
            src="/maka_logo.png"
            alt="MAKA Logo"
            width={120}
            height={60}
            className="object-contain"
            priority
          />
        </div>

        {/* Title and subtitle */}
        <div className="text-center mb-16">
          <h1 className="text-3xl font-bold text-black mb-8">
            Immobilization
          </h1>
          <p className="text-black/70 text-lg">
            Masukkan nama pengguna dan kata sandi
          </p>
        </div>
      </div>

      {/* Bottom section with login form */}
      <div className="bg-black rounded-t-3xl px-8 py-10 min-h-[55vh] flex flex-col">
        <form onSubmit={handleLogin} className="flex-1 flex flex-col justify-center space-y-8">
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 mb-4">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="username" className="sr-only">
              Nama Pengguna
            </Label>
            <div className="relative">
              <User className="absolute left-0 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                id="username"
                type="text"
                placeholder="Nama Pengguna"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="pl-8 h-16 bg-transparent border-0 border-b-2 border-gray-600 rounded-none text-white placeholder-gray-400 focus:border-white focus:ring-0 text-lg"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="sr-only">
              Kata Sandi
            </Label>
            <div className="relative">
              <Lock className="absolute left-0 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Kata Sandi"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-8 pr-10 h-16 bg-transparent border-0 border-b-2 border-gray-600 rounded-none text-white placeholder-gray-400 focus:border-white focus:ring-0 text-lg"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-0 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>


          <div className="text-center mb-8">
            <Button
              type="submit"
              className="w-full h-20 bg-transparent hover:bg-white/10 text-white border-0 rounded-none font-black text-2xl tracking-widest disabled:opacity-50"
              disabled={isLoading}
            >
              {isLoading ? 'MASUK...' : 'MASUK'}
            </Button>
          </div>
        </form>

        {/* Version info */}
        <div className="text-center mt-10">
          <p className="text-gray-400 text-base">V 1.2.3</p>
        </div>
      </div>
    </div>
  );
}
