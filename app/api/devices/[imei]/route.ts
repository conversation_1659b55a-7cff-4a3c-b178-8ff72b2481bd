import { NextRequest, NextResponse } from 'next/server';
import { getDeviceByImei, getImmobilizerLogsByImei } from '@/lib/db/queries';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ imei: string }> }
) {
  try {
    const { imei } = await params;
    const deviceImei = decodeURIComponent(imei);

    // Get device info
    const device = await getDeviceByImei(deviceImei);
    
    // Get immobilizer logs
    const logs = await getImmobilizerLogsByImei(deviceImei, 20); // Last 20 logs

    return NextResponse.json({
      success: true,
      data: {
        device,
        logs,
      },
    });

  } catch (error) {
    console.error('Error fetching device data:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch device data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
