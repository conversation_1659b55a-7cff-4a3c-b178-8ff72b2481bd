import { db, devices, deviceImmobilizerLogs } from '../lib/db';

async function seed() {
  try {
    console.log('🌱 Seeding database...');

    // Insert sample device
    const sampleDevice = await db.insert(devices).values({
      imei: '350424066962463',
      immobilizerStatus: 'engineResume',
      latestTaskId: 'sample-task-id-123',
    }).returning();

    console.log('✅ Sample device created:', sampleDevice[0]);

    // Insert sample logs
    const sampleLogs = await db.insert(deviceImmobilizerLogs).values([
      {
        imei: '350424066962463',
        action: 'engineStop',
        taskId: 'task-id-stop-456',
      },
      {
        imei: '350424066962463',
        action: 'engineResume',
        taskId: 'task-id-resume-789',
      },
    ]).returning();

    console.log('✅ Sample logs created:', sampleLogs.length, 'logs');
    console.log('🎉 Database seeded successfully!');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seed function
seed().then(() => {
  console.log('✨ Seeding completed');
  process.exit(0);
});
