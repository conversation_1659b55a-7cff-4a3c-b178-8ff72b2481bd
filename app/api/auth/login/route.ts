import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password } = body;

    if (!username || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Username and password are required',
        },
        { status: 400 }
      );
    }

    // Make request to the external authentication API
    const response = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/login',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
        }),
        cache: 'no-store',
      }
    );

    if (!response.ok) {
      throw new Error(`Authentication API responded with status: ${response.status}`);
    }

    const data = await response.json();

    // Check if login was successful
    if (!data.token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication failed',
          message: 'Invalid credentials or login failed',
        },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        device_token: data.device_token,
        refresh_token: data.refresh_token,
        token: data.token,
      },
    });

  } catch (error) {
    console.error('Error during authentication:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Authentication failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
