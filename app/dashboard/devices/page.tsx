"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';

import { Loader2, Smartphone, RefreshCw, Eye, MapPin, Battery, Clock } from 'lucide-react';

interface Device {
  id: number;
  imei: string;
  name: string | null;
  deviceId: string | null;
  userStatus: string;
  systemStatus: string;
  latestTaskId: string | null;
  lastSyncAt: string | null;
  // Vehicle data from Track360 API
  status: string | null;
  latitude: string | null;
  longitude: string | null;
  speed: string | null;
  batteryLevel: string | null;
  ignition: string | null;
  lastUpdate: string | null;
  totalDistance: string | null;
  dailyDistance: string | null;
  vehicleType: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function DevicesPage() {
  const [devices, setDevices] = useState<Device[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const { toast } = useToast();

  // Load devices
  useEffect(() => {
    loadDevices();
  }, []);

  const loadDevices = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/devices');
      const result = await response.json();

      if (result.success) {
        setDevices(result.data);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load devices",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading devices:', error);
      toast({
        title: "Error",
        description: "Failed to load devices",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);

      // First, sync devices from vehicles API using admin credentials from database
      const syncResponse = await fetch('/api/devices/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const syncResult = await syncResponse.json();

      if (syncResult.success) {
        toast({
          title: "Sync Successful",
          description: syncResult.message || `Synced ${syncResult.data?.syncedDevices || 0} devices`,
        });
      } else {
        toast({
          title: "Sync Error",
          description: syncResult.error || "Failed to sync devices",
          variant: "destructive",
        });
      }

      // Then reload the devices list
      await loadDevices();

    } catch (error) {
      console.error('Error refreshing devices:', error);
      toast({
        title: "Error",
        description: "Failed to refresh devices",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };



  const getStatusDisplayText = (status: string) => {
    switch (status) {
      case 'engineStop':
        return 'Immobilizer ON';
      case 'engineResume':
        return 'Immobilizer OFF';
      default:
        return status;
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case 'engineStop':
        return 'text-red-600';
      case 'engineResume':
        return 'text-black';
      default:
        return 'text-gray-500';
    }
  };



  const DeviceDetailModal = ({ device }: { device: Device }) => (
    <div className="space-y-6">
      {/* Basic Info */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <h4 className="font-semibold text-sm text-muted-foreground">BASIC INFORMATION</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">Device ID:</span>
              <span className="text-sm font-mono">{device.deviceId || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">IMEI:</span>
              <span className="text-sm font-mono">{device.imei}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Name:</span>
              <span className="text-sm">{device.name || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Type:</span>
              <span className="text-sm">{device.vehicleType || 'N/A'}</span>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-semibold text-sm text-muted-foreground">STATUS</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">User Status:</span>
              <span className={`text-sm font-medium ${getStatusTextColor(device.userStatus)}`}>
                {getStatusDisplayText(device.userStatus)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">System Status:</span>
              <span className={`text-sm font-medium ${getStatusTextColor(device.systemStatus)}`}>
                {getStatusDisplayText(device.systemStatus)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Vehicle Status:</span>
              <span className="text-sm">{device.status || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Ignition:</span>
              <span className="text-sm">{device.ignition || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Battery:</span>
              <div className="flex items-center gap-1">
                <Battery className="h-3 w-3" />
                <span className="text-sm">{device.batteryLevel || 'N/A'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Location & Movement */}
      <div className="space-y-2">
        <h4 className="font-semibold text-sm text-muted-foreground">LOCATION & MOVEMENT</h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm">Location:</span>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span className="text-sm font-mono">
                  {device.latitude && device.longitude
                    ? `${device.latitude}, ${device.longitude}`
                    : 'N/A'
                  }
                </span>
              </div>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Speed:</span>
              <span className="text-sm">{device.speed ? `${device.speed} km/h` : 'N/A'}</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">Total Distance:</span>
              <span className="text-sm">{device.totalDistance ? `${device.totalDistance} km` : 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Daily Distance:</span>
              <span className="text-sm">{device.dailyDistance ? `${device.dailyDistance} km` : 'N/A'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Task & Sync Info */}
      <div className="space-y-2">
        <h4 className="font-semibold text-sm text-muted-foreground">SYNC & TASK INFORMATION</h4>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm">Latest Task ID:</span>
            <span className="text-xs font-mono bg-muted px-2 py-1 rounded">
              {device.latestTaskId || 'N/A'}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm">Last Sync:</span>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span className="text-sm">
                {device.lastSyncAt ? new Date(device.lastSyncAt).toLocaleString() : 'Never'}
              </span>
            </div>
          </div>
          <div className="flex justify-between">
            <span className="text-sm">Last Update (Track360):</span>
            <span className="text-sm">{device.lastUpdate || 'N/A'}</span>
          </div>
        </div>
      </div>

      {/* Timestamps */}
      <div className="pt-4 border-t">
        <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
          <div>
            <strong>Created:</strong> {new Date(device.createdAt).toLocaleString()}
          </div>
          <div>
            <strong>Updated:</strong> {new Date(device.updatedAt).toLocaleString()}
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Devices</h1>
          <p className="text-muted-foreground">
            Manage and monitor all registered devices ({devices.length} total)
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={isRefreshing}
          variant="outline"
        >
          {isRefreshing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Syncing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Sync & Refresh
            </>
          )}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Devices List</CardTitle>
          <CardDescription>
            All registered devices in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {devices.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Smartphone className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No devices found</h3>
              <p className="text-muted-foreground text-center">
                No devices have been registered yet. Devices will appear here once they are added to the system.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">ID</TableHead>
                    <TableHead className="min-w-[180px]">Name</TableHead>
                    <TableHead className="min-w-[140px]">IMEI</TableHead>
                    <TableHead className="w-[110px]">User Status</TableHead>
                    <TableHead className="w-[110px]">System Status</TableHead>
                    <TableHead className="w-[140px]">Last Sync</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {devices.map((device) => (
                    <TableRow key={device.id}>
                      <TableCell className="font-medium">#{device.id}</TableCell>
                      <TableCell className="font-medium">
                        {device.name || 'Unknown Device'}
                      </TableCell>
                      <TableCell className="font-mono text-sm">{device.imei}</TableCell>
                      <TableCell>
                        <span className={`font-medium ${getStatusTextColor(device.userStatus)}`}>
                          {getStatusDisplayText(device.userStatus)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${getStatusTextColor(device.systemStatus)}`}>
                          {getStatusDisplayText(device.systemStatus)}
                        </span>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {device.lastSyncAt ? (
                          new Date(device.lastSyncAt).toLocaleString()
                        ) : (
                          <span className="text-muted-foreground">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedDevice(device)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle className="flex items-center gap-2">
                                <Smartphone className="h-5 w-5" />
                                {device.name || 'Device Details'}
                              </DialogTitle>
                              <DialogDescription>
                                Detailed information for device #{device.id}
                              </DialogDescription>
                            </DialogHeader>
                            <DeviceDetailModal device={device} />
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
