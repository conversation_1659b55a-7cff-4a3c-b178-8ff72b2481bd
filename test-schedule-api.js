// Simple test script for the scheduled job API
// Run with: node test-schedule-api.js

const BASE_URL = 'http://localhost:3000';

async function testScheduleAPI() {
  console.log('Testing Scheduled Job API...\n');

  // Test 1: Get schedule logs (should work even with empty data)
  console.log('1. Testing GET /api/schedule/logs');
  try {
    const response = await fetch(`${BASE_URL}/api/schedule/logs?limit=10`);
    const data = await response.json();
    console.log('✅ Schedule logs API response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.log('❌ Schedule logs API error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Test admin config API
  console.log('2. Testing admin config (to verify credentials are set)');
  try {
    const response = await fetch(`${BASE_URL}/api/admin`);
    const data = await response.json();
    console.log('Response status:', response.status);
    if (data.success && data.data) {
      console.log('✅ Admin config found:', {
        username: data.data.username,
        hasPassword: !!data.data.password,
        latestSyncAt: data.data.latestSyncAt
      });
    } else {
      console.log('❌ No admin config found or API error:', JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.log('❌ Admin config API error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Test devices API to see new status fields
  console.log('3. Testing GET /api/devices (to see new status fields)');
  try {
    const response = await fetch(`${BASE_URL}/api/devices`);
    const data = await response.json();
    console.log('Response status:', response.status);
    if (data.success && data.data.length > 0) {
      console.log('✅ First device data (showing new status fields):');
      const device = data.data[0];
      console.log({
        id: device.id,
        imei: device.imei,
        name: device.name,
        userStatus: device.userStatus,
        systemStatus: device.systemStatus,
        latestTaskId: device.latestTaskId
      });
    } else {
      console.log('No devices found or API error:', JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.log('❌ Devices API error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 4: Test schedule immobilizer API (will fail without valid IMEI)
  console.log('4. Testing POST /api/schedule/immobilizer (with invalid IMEI)');
  try {
    const response = await fetch(`${BASE_URL}/api/schedule/immobilizer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imei: 'test-imei-123'
      })
    });
    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.log('❌ Schedule immobilizer API error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 5: Test schedule immobilizer API without IMEI (should return 400)
  console.log('5. Testing POST /api/schedule/immobilizer (without IMEI)');
  try {
    const response = await fetch(`${BASE_URL}/api/schedule/immobilizer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });
    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.log('❌ Schedule immobilizer API error:', error.message);
  }
}

// Run the test
testScheduleAPI().catch(console.error);
