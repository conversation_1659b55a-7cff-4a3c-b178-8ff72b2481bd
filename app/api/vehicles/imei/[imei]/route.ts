import { NextRequest, NextResponse } from 'next/server';
import { getDeviceByImei } from '@/lib/db/queries';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ imei: string }> }
) {
  try {
    const { imei } = await params;
    const vehicleImei = decodeURIComponent(imei);

    // Get credentials from request headers
    const username = request.headers.get('x-username');
    const password = request.headers.get('x-password');

    if (!username || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication credentials are required',
        },
        { status: 401 }
      );
    }

    // First, check if vehicle exists in database (only non-deleted devices)
    const databaseDevice = await getDeviceByImei(vehicleImei);
    if (!databaseDevice) {
      return NextResponse.json(
        {
          success: false,
          error: 'Vehicle not found in database',
          message: `Vehicle with IMEI ${vehicleImei} is not registered in the system`,
        },
        { status: 404 }
      );
    }

    // Make request to the new pull_api_v2 endpoint with Basic Auth
    const credentials = Buffer.from(`${username}:${password}`).toString('base64');
    const response = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/pull_api_v2?bms=true',
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
        // Add cache control to prevent caching sensitive data
        cache: 'no-store',
      }
    );

    if (!response.ok) {
      throw new Error(`External API responded with status: ${response.status}`);
    }

    const data = await response.json();

    // Check if vehicle data exists
    if (!data.data || data.data.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Vehicle not found',
          message: `No vehicle found with IMEI: ${vehicleImei}`,
        },
        { status: 404 }
      );
    }

    // Find the vehicle with matching IMEI from the array
    const vehicle = data.data.find((v: any) => v.deviceImei === vehicleImei);

    if (!vehicle) {
      return NextResponse.json(
        {
          success: false,
          error: 'Vehicle not found',
          message: `No vehicle found with IMEI: ${vehicleImei}`,
        },
        { status: 404 }
      );
    }

    // Transform the data to match new pull_api_v2 structure
    const vehicleDetail = {
      id: vehicle.deviceId.toString(),
      name: vehicle.vehicleno, // Changed from 'name' to 'vehicleno'
      model: extractModel(vehicle.vehicleno),
      plate: extractPlate(vehicle.vehicleno),
      color: extractColor(vehicle.vehicleno),
      deviceId: vehicle.deviceId,
      deviceImei: vehicle.deviceImei,
      status: vehicle.motion ? 'moving' : 'stopped', // Derive status from motion field
      location: {
        latitude: parseFloat(vehicle.latitude),
        longitude: parseFloat(vehicle.longitude),
      },
      speed: parseFloat(vehicle.speed),
      course: vehicle.course,
      battery: vehicle.battery, // Battery level from pull_api_v2
      ignition: vehicle.ignition, // Ignition status (boolean)
      armed: vehicle.owl_mode === 'on', // Use owl_mode for armed status
      systemStatus: vehicle.owl_mode === 'on' ? 'engineStop' : 'engineResume', // Map owl_mode to system status
      lastUpdate: vehicle.lastUpdate,
      distance: vehicle.distance, // Current distance
      prevOdometer: vehicle.prevOdometer, // Previous odometer reading
      type: vehicle.type,
      motion: vehicle.motion,
      bms: vehicle.bms,
    };

    return NextResponse.json({
      success: true,
      data: vehicleDetail,
    });

  } catch (error) {
    console.error('Error fetching vehicle details:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch vehicle details',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper functions to extract information from vehicle name
function extractModel(name: string): string {
  // Try to extract model from name (e.g., "MAKA Cavalry RnD Victory Red" -> "Cavalry")
  const modelMatch = name.match(/\b(Cavalry|T1800|T1600)\b/i);
  return modelMatch ? modelMatch[1] : 'Unknown';
}

function extractPlate(name: string): string {
  // Try to extract plate number from name
  const plateMatch = name.match(/\b[A-Z]\s*\d{4}\s*[A-Z]{2,3}\b/i);
  return plateMatch ? plateMatch[0] : 'N/A';
}

function extractColor(name: string): string {
  // Try to extract color from name
  const colorMatch = name.match(/\b(Red|Blue|Black|White|Silver|Victory Red)\b/i);
  return colorMatch ? colorMatch[1] : 'Unknown';
}
