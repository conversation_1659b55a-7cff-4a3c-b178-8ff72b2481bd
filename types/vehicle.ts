export interface Vehicle {
  id: string;
  name: string;
  model: string;
  plate: string;
  color: string;
  deviceId: number;
  deviceImei: string;
  status: 'online' | 'offline';
  latitude: string;
  longitude: string;
  speed: string;
  batteryLevel: number;
  ignition: string;
  lastUpdate: string;
  totalDistance: number;
  dailyDistance: number;
  type: string;
}

export interface VehicleDetail extends Vehicle {
  location: {
    latitude: number;
    longitude: number;
  };
  course: string;
  armed: boolean; // Derived from owl_mode
  battery: number; // Changed from batteryLevel
  distance: number; // Changed from totalDistance
  prevOdometer: number;
  motion: boolean;
  bms: any; // Battery Management System data
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface AuthResponse {
  device_token: string;
  refresh_token: string;
  token: string;
}

export interface ImmobilizerRequest {
  device_imei: string;
  action: 'on' | 'off';
  token: string;
}
