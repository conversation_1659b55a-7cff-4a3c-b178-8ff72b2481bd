{"id": "dc659eb2-25f5-49a3-87a0-16f359492046", "prevId": "fd0708a5-bfba-42bb-9e1c-9d6b0ed5c361", "version": "7", "dialect": "postgresql", "tables": {"public.admins": {"name": "admins", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "latest_sync_at": {"name": "latest_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"admins_username_unique": {"name": "admins_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.device_immobilizer_logs": {"name": "device_immobilizer_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "imei": {"name": "imei", "type": "text", "primaryKey": false, "notNull": true}, "vehicle_name": {"name": "vehicle_name", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "previous_status": {"name": "previous_status", "type": "immobilizer_action", "typeSchema": "public", "primaryKey": false, "notNull": false}, "new_status": {"name": "new_status", "type": "immobilizer_action", "typeSchema": "public", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "immobilizer_action", "typeSchema": "public", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.device_schedule_job_logs": {"name": "device_schedule_job_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "imei": {"name": "imei", "type": "text", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "immobilizer_action", "typeSchema": "public", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": false}, "api_response": {"name": "api_response", "type": "text", "primaryKey": false, "notNull": false}, "success": {"name": "success", "type": "text", "primaryKey": false, "notNull": true, "default": "'true'"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "executed_at": {"name": "executed_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.device_task_status_logs": {"name": "device_task_status_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "imei": {"name": "imei", "type": "text", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "status_response": {"name": "status_response", "type": "text", "primaryKey": false, "notNull": false}, "task_status": {"name": "task_status", "type": "text", "primaryKey": false, "notNull": false}, "success": {"name": "success", "type": "text", "primaryKey": false, "notNull": true, "default": "'true'"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "checked_at": {"name": "checked_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.devices": {"name": "devices", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "imei": {"name": "imei", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "device_id": {"name": "device_id", "type": "text", "primaryKey": false, "notNull": false}, "user_status": {"name": "user_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'engineResume'"}, "system_status": {"name": "system_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'engineResume'"}, "latest_task_id": {"name": "latest_task_id", "type": "text", "primaryKey": false, "notNull": false}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "latitude": {"name": "latitude", "type": "text", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "text", "primaryKey": false, "notNull": false}, "speed": {"name": "speed", "type": "text", "primaryKey": false, "notNull": false}, "battery_level": {"name": "battery_level", "type": "text", "primaryKey": false, "notNull": false}, "ignition": {"name": "ignition", "type": "text", "primaryKey": false, "notNull": false}, "last_update": {"name": "last_update", "type": "text", "primaryKey": false, "notNull": false}, "total_distance": {"name": "total_distance", "type": "text", "primaryKey": false, "notNull": false}, "daily_distance": {"name": "daily_distance", "type": "text", "primaryKey": false, "notNull": false}, "vehicle_type": {"name": "vehicle_type", "type": "text", "primaryKey": false, "notNull": false}, "course": {"name": "course", "type": "text", "primaryKey": false, "notNull": false}, "motion": {"name": "motion", "type": "text", "primaryKey": false, "notNull": false}, "owl_mode": {"name": "owl_mode", "type": "text", "primaryKey": false, "notNull": false}, "bms": {"name": "bms", "type": "text", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"devices_imei_unique": {"name": "devices_imei_unique", "nullsNotDistinct": false, "columns": ["imei"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.immobilizer_action": {"name": "immobilizer_action", "schema": "public", "values": ["engineStop", "engineResume"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}