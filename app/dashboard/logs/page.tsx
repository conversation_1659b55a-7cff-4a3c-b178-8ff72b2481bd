"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DataTablePagination } from '@/components/ui/data-table-pagination';
import { useToast } from '@/hooks/use-toast';
import { Loader2, FileText, RefreshCw, Search } from 'lucide-react';

interface DeviceLog {
  id: number;
  imei: string;
  vehicleName: string | null;
  username: string | null;
  previousStatus: 'engineStop' | 'engineResume' | null;
  newStatus: 'engineStop' | 'engineResume' | null;
  action: 'engineStop' | 'engineResume';
  taskId: string | null;
  taskData: any; // JSONB data from API response
  createdAt: string;
  updatedAt: string;
}

interface PaginationInfo {
  page: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export default function LogsPage() {
  const [logs, setLogs] = useState<DeviceLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<DeviceLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [limit, setLimit] = useState(100); // Keep for legacy filter
  const { toast } = useToast();

  const getStatusDisplayText = (status: string | null) => {
    if (!status) return 'N/A';
    switch (status) {
      case 'engineStop':
        return 'Immobilizer ON';
      case 'engineResume':
        return 'Immobilizer OFF';
      default:
        return status;
    }
  };

  const getStatusTextColor = (status: string | null) => {
    if (!status) return 'text-gray-500';
    switch (status) {
      case 'engineStop':
        return 'text-red-600';
      case 'engineResume':
        return 'text-black';
      default:
        return 'text-gray-500';
    }
  };

  // Load logs with pagination
  useEffect(() => {
    loadLogs();
  }, [currentPage, pageSize]);

  // Load logs with legacy limit (for backward compatibility)
  useEffect(() => {
    if (limit !== 100) {
      loadLogsLegacy();
    }
  }, [limit]);

  // Filter logs based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredLogs(logs);
    } else {
      const filtered = logs.filter(log => 
        log.imei.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (log.taskId && log.taskId.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredLogs(filtered);
    }
  }, [logs, searchTerm]);

  const loadLogs = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/logs?page=${currentPage}&pageSize=${pageSize}`);
      const result = await response.json();

      if (result.success) {
        setLogs(result.data);
        setPagination(result.pagination);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load logs",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading logs:', error);
      toast({
        title: "Error",
        description: "Failed to load logs",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadLogsLegacy = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/logs?limit=${limit}`);
      const result = await response.json();

      if (result.success) {
        setLogs(result.data);
        // Reset pagination when using legacy mode
        setPagination({
          page: 1,
          pageSize: result.data.length,
          totalCount: result.data.length,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load logs",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading logs:', error);
      toast({
        title: "Error",
        description: "Failed to load logs",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      if (limit !== 100) {
        await loadLogsLegacy();
      } else {
        await loadLogs();
      }
      toast({
        title: "Success",
        description: "Logs refreshed successfully",
      });
    } catch (error) {
      console.error('Error refreshing logs:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };





  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Audit Logs</h1>
          <p className="text-muted-foreground">
            {limit === 100 ? (
              `View all user immobilizer action logs (${pagination.totalCount} total records)`
            ) : (
              `View all user immobilizer action logs (${filteredLogs.length} of ${logs.length} shown)`
            )}
          </p>
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={isRefreshing}
          variant="outline"
        >
          {isRefreshing ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by IMEI, action, or task ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="limit">Limit</Label>
              <Input
                id="limit"
                type="number"
                min="1"
                max="1000"
                value={limit}
                onChange={(e) => setLimit(parseInt(e.target.value) || 100)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Device Logs</CardTitle>
          <CardDescription>
            All device immobilizer action logs
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredLogs.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {logs.length === 0 ? 'No logs found' : 'No matching logs'}
              </h3>
              <p className="text-muted-foreground text-center">
                {logs.length === 0
                  ? 'No device logs have been recorded yet.'
                  : 'Try adjusting your search criteria to find logs.'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[80px]">ID</TableHead>
                      <TableHead className="min-w-[150px]">Vehicle</TableHead>
                      <TableHead className="min-w-[140px]">IMEI</TableHead>
                      <TableHead className="w-[120px]">User</TableHead>
                      <TableHead className="w-[140px]">From Status</TableHead>
                      <TableHead className="w-[140px]">To Status</TableHead>
                      <TableHead className="w-[140px]">Created</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell className="font-medium">#{log.id}</TableCell>
                        <TableCell className="font-medium">
                          {log.vehicleName || 'Unknown Vehicle'}
                        </TableCell>
                        <TableCell className="font-mono text-sm">{log.imei}</TableCell>
                        <TableCell className="font-medium">
                          {log.username || 'System'}
                        </TableCell>
                        <TableCell>
                          <span className={`font-medium ${getStatusTextColor(log.previousStatus)}`}>
                            {getStatusDisplayText(log.previousStatus)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className={`font-medium ${getStatusTextColor(log.newStatus)}`}>
                            {getStatusDisplayText(log.newStatus)}
                          </span>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {new Date(log.createdAt).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination Controls */}
              {limit === 100 && (
                <DataTablePagination
                  pagination={pagination}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  isLoading={isLoading}
                />
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
