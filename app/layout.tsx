import type { <PERSON>ada<PERSON> } from "next";
import { StackProvider, StackTheme } from "@stackframe/stack";
import { stackServerApp } from "../stack";
import { AuthProvider } from "@/contexts/AuthContext";
import { <PERSON>abin } from "next/font/google";
import "./globals.css";

const cabin = Cabin({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-cabin'
});

export const metadata: Metadata = {
  title: "MAKA Dashboard",
  description: "Vehicle tracking and immobilizer control dashboard",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={cabin.variable}>
        <StackProvider app={stackServerApp}>
          <StackTheme>
            <AuthProvider>
              {children}
            </AuthProvider>
          </StackTheme>
        </StackProvider>
      </body>
    </html>
  );
}
