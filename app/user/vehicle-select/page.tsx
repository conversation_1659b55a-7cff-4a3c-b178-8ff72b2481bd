'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Loader2, Search } from 'lucide-react';
import Image from 'next/image';
import { Vehicle, ApiResponse } from '@/types/vehicle';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import VehicleSelectHeader from '@/components/headers/VehicleSelectHeader';

function VehicleSelectContent() {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [filteredVehicles, setFilteredVehicles] = useState<Vehicle[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { credentials } = useAuth();



  // Fetch vehicles from API
  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get credentials from auth context
        if (!credentials) {
          throw new Error('Authentication credentials not available');
        }

        const response = await fetch('/api/vehicles', {
          headers: {
            'x-username': credentials.username,
            'x-password': credentials.password,
          },
        });
        const data: ApiResponse<Vehicle[]> = await response.json();

        if (!response.ok) {
          throw new Error(data.message || 'Failed to fetch vehicles');
        }

        if (data.success && data.data) {
          setVehicles(data.data);
          setFilteredVehicles(data.data);
        } else {
          throw new Error(data.error || 'Failed to load vehicles');
        }
      } catch (err) {
        console.error('Error fetching vehicles:', err);
        setError(err instanceof Error ? err.message : 'Failed to load vehicles');
      } finally {
        setLoading(false);
      }
    };

    fetchVehicles();
  }, [credentials]);

  const handleVehicleCardClick = (vehicle: Vehicle) => {
    // Navigate directly to vehicle info when clicking on vehicle card
    router.push(`/user/vehicle-info?imei=${encodeURIComponent(vehicle.deviceImei)}`);
  };



  // Handle search functionality
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value.toLowerCase();
    setSearchQuery(query);

    if (query.trim() === '') {
      setFilteredVehicles(vehicles);
    } else {
      const filtered = vehicles.filter(vehicle =>
        vehicle.name.toLowerCase().includes(query) ||
        vehicle.deviceImei.toLowerCase().includes(query) ||
        getVIN(vehicle).toLowerCase().includes(query)
      );
      setFilteredVehicles(filtered);
    }
  };

  // Function to get motor image based on vehicle name
  const getMotorImage = (vehicle: Vehicle) => {
    const vehicleName = vehicle.name?.toLowerCase() || '';

    // Check if vehicle name contains color keywords from API
    if (vehicleName.includes('red')) {
      return '/maka_beige_red.png';
    } else if (vehicleName.includes('white')) {
      return '/maka_beige_white.png';
    } else if (vehicleName.includes('beige')) {
      return '/maka_beige_brown.png';
    } else {
      // Default to beige if no color found
      return '/maka_beige_brown.png';
    }
  };

  // Function to get color from vehicle name
  const getVehicleColor = (vehicle: Vehicle) => {
    const vehicleName = vehicle.name?.toLowerCase() || '';

    // Check API vehicle name for color keywords
    if (vehicleName.includes('red')) return 'RED';
    if (vehicleName.includes('white')) return 'WHITE';
    if (vehicleName.includes('beige')) return 'BEIGE';

    // Default to BEIGE if no color found
    return 'BEIGE';
  };

  // Function to get card background color
  const getCardBackgroundColor = () => {
    return 'bg-white'; // All cards use white background
  };

  // Function to extract VIN from vehicle name or generate placeholder
  const getVIN = (vehicle: Vehicle) => {
    // Generate VIN based on device ID with proper formatting
    return `${vehicle.deviceId.toString().padStart(11, '0')}`;
  };





  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      {/* Mobile Header */}
      <VehicleSelectHeader />

      {/* Content */}
      <div className="flex-1 flex flex-col px-4 py-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Pilih Motor
          </h1>
          <p className="text-gray-600 text-sm">
            Data Anda dilindungi dan tidak akan disebarluaskan
          </p>
        </div>

        {/* Search Input */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Select vehicle"
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-full h-12 bg-white border border-gray-300 rounded-lg pl-10 pr-4 text-sm text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
            />
          </div>
        </div>

        {/* Vehicle Cards */}
        <div className="flex-1">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading vehicles...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="text-blue-600 border-blue-600 hover:bg-blue-50"
              >
                Try Again
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredVehicles.map((vehicle, index) => (
                <div
                  key={`vehicle-card-${vehicle.id}-${index}`}
                  onClick={() => handleVehicleCardClick(vehicle)}
                  className={`${getCardBackgroundColor(vehicle)} rounded-xl shadow-sm border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow relative min-h-[120px]`}
                >
                  <div className="flex items-start justify-between h-full">
                    {/* Vehicle Info - Left Side */}
                    <div className="flex-1 min-w-0 pr-16">
                      {/* Brand and Color */}
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-xl font-black text-black" style={{ fontFamily: 'Arial Black, sans-serif' }}>
                          Cavalry
                        </span>
                        <span className="text-xl font-bold text-black">
                          {getVehicleColor(vehicle)}
                        </span>
                      </div>

                      {/* VIN */}
                      <div>
                        <span className="font-cabin font-bold text-black underline" style={{
                          fontSize: '10px',
                          lineHeight: '100%',
                          letterSpacing: '0px'
                        }}>
                          VIN: {getVIN(vehicle)}
                        </span>
                      </div>

                      {/* IMEI */}
                      <div className="mb-3">
                        <span className="font-cabin font-bold text-black underline" style={{
                          fontSize: '10px',
                          lineHeight: '100%',
                          letterSpacing: '0px'
                        }}>
                          IMEI: {vehicle.deviceImei}
                        </span>
                      </div>

                      {/* Owner Name */}
                      <div className="text-black" style={{
                        fontFamily: 'Cabin',
                        fontWeight: '700',
                        fontSize: '10px',
                        lineHeight: '140%',
                        letterSpacing: '0px',
                        verticalAlign: 'middle'
                      }}>
                        {vehicle.name}
                      </div>
                    </div>

                    {/* Motor Image - Right Side */}
                    <div className="flex-shrink-0 absolute right-0 bottom-0">
                      <Image
                        src={getMotorImage(vehicle)}
                        alt={`${vehicle.name} motor`}
                        width={200}
                        height={75}
                        className="object-contain"
                      />
                    </div>
                  </div>
                </div>
              ))}
              {filteredVehicles.length === 0 && searchQuery && (
                <div className="text-center py-8">
                  <p className="text-gray-500">No vehicles found matching "{searchQuery}"</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function VehicleSelectPage() {
  return (
    <ProtectedRoute>
      <VehicleSelectContent />
    </ProtectedRoute>
  );
}
