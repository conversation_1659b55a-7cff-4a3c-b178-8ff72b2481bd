import { NextRequest, NextResponse } from 'next/server';
import { getAllDevices } from '@/lib/db/queries';

export async function GET(request: NextRequest) {
  try {
    // Get credentials from request headers
    const username = request.headers.get('x-username');
    const password = request.headers.get('x-password');

    if (!username || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication credentials are required',
        },
        { status: 401 }
      );
    }

    // First, get all devices from database to filter API results (only non-deleted devices)
    const databaseDevices = await getAllDevices();
    const allowedImeis = new Set(databaseDevices.map(device => device.imei));

    // Make request to the new pull_api_v2 endpoint with Basic Auth
    const credentials = Buffer.from(`${username}:${password}`).toString('base64');
    const response = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/pull_api_v2?bms=true',
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
        // Add cache control to prevent caching sensitive data
        cache: 'no-store',
      }
    );

    if (!response.ok) {
      throw new Error(`External API responded with status: ${response.status}`);
    }

    const data = await response.json();

    // Log the raw response for debugging
    console.log('=== Track360 API Response (pull_api_v2) ===');
    console.log('Success count:', data.success_count);
    console.log('Error count:', data.error_count);
    console.log('Number of devices:', data.data?.length);
    console.log('Device names:', data.data?.map((d: any) => d.vehicleno));
    console.log('Raw response:', JSON.stringify(data, null, 2));

    // Check if vehicle data exists
    if (!data.data || data.data.length === 0) {
      return NextResponse.json({
        success: true,
        data: [],
      });
    }

    // Filter API data to only include vehicles that exist in database
    const filteredApiData = data.data.filter((vehicle: any) =>
      allowedImeis.has(vehicle.deviceImei)
    );

    // Transform the filtered data to match new pull_api_v2 structure
    const vehicles = filteredApiData.map((vehicle: any) => ({
      id: vehicle.deviceId.toString(),
      name: vehicle.vehicleno, // Vehicle name from pull_api_v2
      model: extractModel(vehicle.vehicleno),
      plate: extractPlate(vehicle.vehicleno),
      color: extractColor(vehicle.vehicleno),
      deviceId: vehicle.deviceId,
      deviceImei: vehicle.deviceImei,
      status: vehicle.motion ? 'moving' : 'stopped', // Derive status from motion field
      latitude: vehicle.latitude,
      longitude: vehicle.longitude,
      speed: vehicle.speed,
      batteryLevel: vehicle.battery, // Battery level from pull_api_v2
      ignition: vehicle.ignition, // Ignition status (boolean)
      lastUpdate: vehicle.lastUpdate,
      totalDistance: vehicle.prevOdometer, // Use prevOdometer as total distance
      dailyDistance: vehicle.distance, // Use distance as daily distance
      type: vehicle.type,
      systemStatus: vehicle.owl_mode === 'on' ? 'engineStop' : 'engineResume', // Map owl_mode to system status
    }));

    return NextResponse.json({
      success: true,
      data: vehicles,
    });

  } catch (error) {
    console.error('Error fetching vehicles:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch vehicles',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper functions to extract information from vehicle name
function extractModel(name: string): string {
  // Try to extract model from name (e.g., "MAKA Cavalry RnD Victory Red" -> "Cavalry")
  const modelMatch = name.match(/\b(Cavalry|T1800|T1600)\b/i);
  return modelMatch ? modelMatch[1] : 'Unknown';
}

function extractPlate(name: string): string {
  // Try to extract plate number from name
  const plateMatch = name.match(/\b[A-Z]\s*\d{4}\s*[A-Z]{2,3}\b/i);
  return plateMatch ? plateMatch[0] : 'N/A';
}

function extractColor(name: string): string {
  // Try to extract color from name
  const colorMatch = name.match(/\b(Red|Blue|Black|White|Silver|Victory Red)\b/i);
  return colorMatch ? colorMatch[1] : 'Unknown';
}
