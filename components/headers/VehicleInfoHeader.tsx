'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import Image from 'next/image';

export default function VehicleInfoHeader() {
  const router = useRouter();

  const handleBack = () => {
    router.push('/user/vehicle-select');
  };

  return (
    <div className="shadow-sm" style={{ backgroundColor: '#24CFFA' }}>
      <div className="flex items-center justify-between px-4 py-4">
        <button
          onClick={handleBack}
          className="w-10 h-10 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors"
          title="Back"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <Image
          src="/maka_logo.png"
          alt="MAKA Motors"
          width={50}
          height={16}
          className="h-4 w-auto"
        />
      </div>
    </div>
  );
}
