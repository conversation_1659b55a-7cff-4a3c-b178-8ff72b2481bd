import { NextResponse } from 'next/server';
import { getAllDevices } from '@/lib/db/queries';

export async function GET() {
  try {
    const devices = await getAllDevices();
    
    return NextResponse.json({
      success: true,
      data: devices,
      count: devices.length,
    });

  } catch (error) {
    console.error('Error fetching devices:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch devices',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
