import { NextRequest, NextResponse } from 'next/server';
import { getAllImmobilizerLogs, getAllImmobilizerLogsWithPagination } from '@/lib/db/queries';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get('limit');
    const pageParam = searchParams.get('page');
    const pageSizeParam = searchParams.get('pageSize');

    // Check if pagination is requested
    if (pageParam || pageSizeParam) {
      const page = pageParam ? parseInt(pageParam, 10) : 1;
      const pageSize = pageSizeParam ? parseInt(pageSizeParam, 10) : 20;

      // Validate pagination parameters
      if (isNaN(page) || page < 1) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid page parameter. Must be a positive integer.',
          },
          { status: 400 }
        );
      }

      if (isNaN(pageSize) || pageSize < 1 || pageSize > 100) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid pageSize parameter. Must be between 1 and 100.',
          },
          { status: 400 }
        );
      }

      const result = await getAllImmobilizerLogsWithPagination(page, pageSize);

      return NextResponse.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    }

    // Legacy limit-based API for backward compatibility
    const limit = limitParam ? parseInt(limitParam, 10) : 100;

    // Validate limit
    if (isNaN(limit) || limit < 1 || limit > 1000) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid limit parameter. Must be between 1 and 1000.',
        },
        { status: 400 }
      );
    }

    const logs = await getAllImmobilizerLogs(limit);

    return NextResponse.json({
      success: true,
      data: logs,
      count: logs.length,
      limit,
    });

  } catch (error) {
    console.error('Error fetching logs:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch logs',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
