import { NextRequest, NextResponse } from 'next/server';
import { upsertDeviceUserStatus, createImmobilizerLog } from '@/lib/db/queries';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { device_imei, action, token } = body;

    if (!device_imei || !action || !token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device IMEI, action, and token are required',
        },
        { status: 400 }
      );
    }

    if (!['on', 'off'].includes(action.toLowerCase())) {
      return NextResponse.json(
        {
          success: false,
          error: 'Action must be either "on" or "off"',
        },
        { status: 400 }
      );
    }

    // Determine the type based on action
    const type = action.toLowerCase() === 'on' ? 'engineStop' : 'engineResume';

    // Make request to the external immobilizer API
    const response = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/set_owl_mode_v1',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token, // Token should already include "Bearer " prefix
        },
        body: JSON.stringify({
          device_imei,
          type,
        }),
        cache: 'no-store',
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Immobilizer API error:', response.status, errorText);

      if (response.status === 401) {
        return NextResponse.json(
          {
            success: false,
            error: 'Authentication failed',
            message: 'Token expired or invalid. Please login again.',
          },
          { status: 401 }
        );
      }

      throw new Error(`Immobilizer API responded with status: ${response.status}`);
    }

    const data = await response.json();

    // Extract task_id from response
    const taskId = data.task_id;

    // Save to database
    try {
      // Update device user status with task_id
      await upsertDeviceUserStatus(device_imei, type, taskId);

      // Log the action with task_id and task_data
      await createImmobilizerLog({
        imei: device_imei,
        action: type,
        taskId: taskId,
        taskData: data, // Store full API response
      });
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Continue even if database fails - don't block the immobilizer command
    }

    return NextResponse.json({
      success: true,
      data: {
        ...data,
        task_id: taskId,
      },
      message: `Immobilizer ${action.toLowerCase() === 'on' ? 'activated' : 'deactivated'} successfully`,
    });

  } catch (error) {
    console.error('Error controlling immobilizer:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to control immobilizer',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
