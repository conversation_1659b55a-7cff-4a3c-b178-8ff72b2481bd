# MAKA Dashboard

A Next.js dashboard application for vehicle tracking and immobilizer control.

## Features

- Vehicle tracking and monitoring
- Immobilizer control (ON/OFF)
- Real-time vehicle data from Track360 API
- Database logging of immobilizer commands
- Authentication system

## Prerequisites

- Node.js 18+ or Bun
- PostgreSQL database
- Track360 API access

## Setup

### 1. Install Dependencies

```bash
bun install
```

### 2. Environment Configuration

Copy the `.env` file and update the database configuration:

```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database
```

Replace the placeholder values with your actual PostgreSQL connection details:
- `username`: Your PostgreSQL username
- `password`: Your PostgreSQL password
- `host`: Database host (e.g., localhost)
- `port`: Database port (default: 5432)
- `database`: Database name (e.g., maka_dashboard)

### 3. Database Setup

#### Generate Migration Files
```bash
bun run db:generate
```

#### Run Migrations
```bash
bun run db:migrate
```

#### Alternative: Push Schema Directly (for development)
```bash
bun run db:push
```

#### View Database (Optional)
```bash
bun run db:studio
```

### 4. Database Schema

The application creates three main tables:

#### `devices` table:
- `id` (serial, primary key)
- `imei` (text, unique) - Device IMEI number
- `user_status` (text) - Status set by user: 'engineStop' or 'engineResume'
- `system_status` (text) - Status from Track360 API: 'engineStop' or 'engineResume'
- `latest_task_id` (text) - Latest task ID from immobilizer API
- `name` (text) - Vehicle name from Track360 (`vehicleno` field)
- `device_id` (text) - Device ID from Track360 (`deviceId` field)
- `last_sync_at` (timestamp) - Last sync timestamp
- Vehicle data from Track360 API pull_v2:
  - `status` (text) - Derived vehicle status (moving/stopped)
  - `latitude`, `longitude` (text) - GPS coordinates
  - `speed` (text) - Vehicle speed
  - `battery_level` (text) - Battery level (from `battery` field)
  - `ignition` (text) - Ignition status (boolean)
  - `last_update` (text) - Last update timestamp from API
  - `total_distance` (text) - Total distance (from `prevOdometer`)
  - `daily_distance` (text) - Daily distance (from `distance`)
  - `vehicle_type` (text) - Vehicle type (from `type`)
  - `course` (text) - Course/direction
  - `motion` (text) - Motion status (boolean)
  - `owl_mode` (text) - Owl mode status (on/off)
  - `bms` (text) - BMS data as JSON string
- `deleted_at` (timestamp) - Soft delete timestamp (null = active, not null = deleted)
- `created_at` (timestamp)
- `updated_at` (timestamp)

#### `device_immobilizer_logs` table:
- `id` (serial, primary key)
- `imei` (text) - Device IMEI number
- `action` (enum) - Action performed: 'engineStop' or 'engineResume'
- `task_id` (text) - Task ID from immobilizer API response
- `created_at` (timestamp)
- `updated_at` (timestamp)

#### `device_schedule_job_logs` table:
- `id` (serial, primary key)
- `imei` (text) - Device IMEI number
- `action` (enum) - Action performed: 'engineStop' or 'engineResume'
- `task_id` (text) - Task ID from immobilizer API response
- `api_response` (text) - Full API response as JSON string
- `success` (text) - 'true' or 'false' indicating job success
- `error_message` (text) - Error message if failed
- `executed_at` (timestamp) - When the job was executed
- `created_at` (timestamp)
- `updated_at` (timestamp)

#### `device_task_status_logs` table:
- `id` (serial, primary key)
- `imei` (text) - Device IMEI number
- `task_id` (text) - Task ID to check status for
- `status_response` (text) - Full task status API response as JSON string
- `task_status` (text) - Extracted task status from response
- `success` (text) - 'true' or 'false' indicating status check success
- `error_message` (text) - Error message if failed
- `checked_at` (timestamp) - When the status was checked
- `created_at` (timestamp)
- `updated_at` (timestamp)

#### `admins` table:
- `id` (serial, primary key)
- `email` (text, unique) - Admin email address
- `password` (text) - Admin password (should be hashed)
- `latest_sync_at` (timestamp) - Last synchronization timestamp
- `created_at` (timestamp)
- `updated_at` (timestamp)

## Development

### Start Development Server

```bash
bun run dev
```

### Available Scripts

- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run start` - Start production server
- `bun run lint` - Run ESLint
- `bun run db:generate` - Generate migration files
- `bun run db:migrate` - Run database migrations
- `bun run db:push` - Push schema to database (development)
- `bun run db:studio` - Open Drizzle Studio

## Soft Delete and Data Synchronization

The application implements soft delete functionality for device management:

### Soft Delete Pattern
- Devices are not permanently deleted from the database
- Instead, they are marked with a `deleted_at` timestamp
- Soft deleted devices are excluded from all queries and API responses
- If a device appears again in the Track360 API, it will be restored (deleted_at set to null)

### Synchronization Process
1. **Manual Sync**: Click "Sync & Refresh" button in the dashboard devices page
2. **Auto Sync**: Automatically triggered when saving admin configuration

### Sync Behavior
- Fetches data from Track360 API using `pull_api_v2` endpoint
- Updates existing devices with latest data from API
- Creates new devices if they don't exist in database
- Soft deletes devices that are no longer present in API response
- Restores previously soft-deleted devices if they reappear in API
- Maps `owl_mode` from API to `system_status`:
  - `owl_mode = "off"` → `system_status = "engineResume"` (Immobilizer OFF)
  - `owl_mode = "on"` → `system_status = "engineStop"` (Immobilizer ON)

### Data Filtering
- All user-facing APIs only return non-deleted devices
- Vehicle lists and details are filtered by database existence
- Only devices present in local database are shown to users

### Status Management
- **User Status**: Set by user when controlling immobilizer (updated via immobilizer API)
- **System Status**: Automatically updated from Track360 API `owl_mode` field during sync
- Status display in dashboard shows user-friendly text:
  - `engineStop` → "Immobilizer ON"
  - `engineResume` → "Immobilizer OFF"

### Status Management
- **User Status**: Set by user when controlling immobilizer (updated via immobilizer API)
- **System Status**: Automatically updated from Track360 API `owl_mode` field during sync
- Status display in dashboard shows user-friendly text:
  - `engineStop` → "Stopped"
  - `engineResume` → "Running"

## API Endpoints

### Authentication
- `POST /api/auth/login` - Login with Track360 credentials

### Vehicles
- `GET /api/vehicles` - Get all vehicles
- `GET /api/vehicles/imei/[imei]` - Get vehicle details by IMEI
- `POST /api/vehicles/immobilizer` - Control vehicle immobilizer
- `GET /api/vehicles/immobilizer/status/[imei]` - Get immobilizer status from database

### Devices
- `GET /api/devices` - Get all devices with user_status and system_status
- `GET /api/devices/[imei]` - Get device status and immobilizer logs
- `POST /api/devices/sync` - Sync devices from Track360 API with soft delete

### Scheduled Jobs (N8N Integration)
- `POST /api/schedule/immobilizer` - Execute immobilizer command based on device user_status
- `GET /api/schedule/logs` - Get scheduled job execution logs
- `POST /api/schedule/task-status` - Check task status for a given task_id
- `GET /api/schedule/task-status-logs` - Get task status check logs

## Immobilizer Status Management

The application uses dual status tracking for immobilizer control:

1. **user_status**: Status set by user through the dashboard (engineStop/engineResume)
2. **system_status**: Status retrieved from Track360 API (engineStop/engineResume)

### Status Flow:
1. User toggles immobilizer → `user_status` updated → Command sent to Track360 API → Receive task_id response
2. Status and task_id saved to database → UI displays status from database
3. Database automatically creates device record if it doesn't exist
4. All commands are logged with task_id and timestamps for audit trail

### Task ID Tracking:
- **API Response**: Track360 API returns `{"success": true, "task_id": "uuid"}`
- **Database Storage**: task_id stored in both `devices.latest_task_id` and `device_immobilizer_logs.task_id`
- **UI Display**: Shows current task_id for debugging and tracking
- **Audit Trail**: Complete history of all task_ids for each command

## Dashboard Features

### Devices Page (`/dashboard/devices`)
- **Dual Status Display**: Shows both `user_status` and `system_status` in separate columns
- **Device Details Modal**: Click the eye icon to view detailed device information including both status types
- **Sync & Refresh**: Updates device data from Track360 API
- **Real-time Status**: Color-coded badges for easy status identification

### Schedule Job Logs Page (`/dashboard/schedule-logs`)
- **Execution History**: View all scheduled job executions triggered by N8N
- **Success/Failure Tracking**: Visual indicators for job success or failure
- **Detailed Logs**: Click to view full API responses, error messages, and task IDs
- **Real-time Updates**: Refresh to see latest job executions

## Scheduled Job Integration (N8N)

The dashboard provides scheduled job functionality for automated immobilizer control:

### How it works:
1. **N8N Setup**: Configure N8N to call `POST /api/schedule/immobilizer` with device IMEI
2. **Status Check**: API retrieves device from database and checks `user_status`
3. **Authentication**: API authenticates with Track360 using admin credentials from database
4. **Command Execution**: API sends immobilizer command based on `user_status` to Track360 API
5. **Logging**: All execution results are logged in `device_schedule_job_logs` table
6. **Task ID Update**: Device's `latest_task_id` is updated if command succeeds
7. **Task Status Check**: If a `task_id` is received, the API automatically checks task status via Track360's task status API
8. **Status Logging**: Task status check results are logged in `device_task_status_logs` table

### API Usage:

#### Execute Scheduled Command
```bash
POST /api/schedule/immobilizer
Content-Type: application/json

{
  "imei": "device_imei_here"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "imei": "device_imei_here",
    "action": "engineStop",
    "userStatus": "engineStop",
    "taskId": "task_id_from_api",
    "apiResponse": {...}
  },
  "message": "Scheduled immobilizer command executed successfully: engineStop"
}
```

#### Get Job Logs
```bash
GET /api/schedule/logs?limit=100
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "imei": "device_imei",
      "action": "engineStop",
      "taskId": "uuid",
      "apiResponse": "{...}",
      "success": "true",
      "errorMessage": null,
      "executedAt": "2024-01-01T12:00:00Z",
      "createdAt": "2024-01-01T12:00:00Z"
    }
  ],
  "count": 1,
  "limit": 100
}
```

#### Check Task Status
```bash
POST /api/schedule/task-status
Content-Type: application/json

{
  "imei": "device_imei_here",
  "taskId": "task_id_to_check"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "imei": "device_imei_here",
    "taskId": "task_id_to_check",
    "taskStatus": "failed",
    "response": {
      "data": {
        "message": "Device Offline",
        "status": "failed"
      },
      "state": "SUCCESS"
    },
    "checkedAt": "2024-01-01T12:05:00Z"
  }
}
```

#### Get Task Status Logs
```bash
GET /api/schedule/task-status-logs?limit=100
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "imei": "device_imei_here",
      "taskId": "task_id_checked",
      "statusResponse": "{\"data\":{\"message\":\"Device Offline\",\"status\":\"failed\"},\"state\":\"SUCCESS\"}",
      "taskStatus": "failed",
      "success": "true",
      "errorMessage": null,
      "checkedAt": "2024-01-01T12:05:00Z",
      "createdAt": "2024-01-01T12:05:00Z",
      "updatedAt": "2024-01-01T12:05:00Z"
    }
  ],
  "count": 1
}
```

## Database Migration Commands

### Initial Setup
```bash
# Generate initial migration
bun run db:generate

# Apply migrations to database
bun run db:migrate
```

### Making Schema Changes
```bash
# 1. Modify schema in lib/db/schema.ts
# 2. Generate new migration
bun run db:generate

# 3. Apply migration
bun run db:migrate
```

### Development Workflow
```bash
# For quick development (pushes schema directly)
bun run db:push

# For production (use migrations)
bun run db:generate && bun run db:migrate
```

## Troubleshooting

### Database Connection Issues
1. Verify PostgreSQL is running
2. Check DATABASE_URL format in .env
3. Ensure database exists
4. Verify user permissions

### Migration Issues
```bash
# Reset migrations (development only)
rm -rf drizzle/
bun run db:generate
bun run db:push
```

## Tech Stack

- **Framework**: Next.js 15
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Custom auth with Track360 API
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Package Manager**: Bun
