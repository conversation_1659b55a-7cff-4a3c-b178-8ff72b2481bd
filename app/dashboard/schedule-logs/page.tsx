'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DataTablePagination } from '@/components/ui/data-table-pagination';
import { useToast } from '@/hooks/use-toast';
import { Loader2, RefreshCw, Eye, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface ScheduleJobLog {
  id: number;
  imei: string;
  action: 'engineStop' | 'engineResume';
  trigger: string; // 'api' or 'user'
  taskId: string | null;
  apiResponse: string | null;
  taskData: any; // JSONB data from task status API
  success: string;
  errorMessage: string | null;
  executedAt: string;
  createdAt: string;
  updatedAt: string;
}

interface PaginationInfo {
  page: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export default function ScheduleLogsPage() {
  const [logs, setLogs] = useState<ScheduleJobLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedLog, setSelectedLog] = useState<ScheduleJobLog | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const { toast } = useToast();

  const loadLogs = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/schedule/logs?page=${currentPage}&pageSize=${pageSize}`);
      const result = await response.json();

      if (result.success) {
        setLogs(result.data);
        setPagination(result.pagination);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load schedule job logs",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading schedule job logs:', error);
      toast({
        title: "Error",
        description: "Failed to load schedule job logs",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await loadLogs();
      toast({
        title: "Refreshed",
        description: "Schedule job logs have been refreshed",
      });
    } catch (error) {
      console.error('Error refreshing logs:', error);
      toast({
        title: "Error",
        description: "Failed to refresh logs",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadLogs();
  }, [currentPage, pageSize]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const getSuccessBadgeVariant = (success: string) => {
    if (success === 'true') return 'default';
    if (success === 'pending') return 'secondary';
    return 'destructive';
  };

  const getSuccessIcon = (success: string) => {
    if (success === 'true') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    } else if (success === 'pending') {
      return <Clock className="h-4 w-4 text-blue-600" />;
    } else {
      return <XCircle className="h-4 w-4 text-red-600" />;
    }
  };

  const getSuccessText = (success: string) => {
    if (success === 'true') return 'Success';
    if (success === 'pending') return 'Pending';
    return 'Failed';
  };



  const getActionBadgeVariant = (action: string) => {
    return action === 'engineStop' ? 'destructive' : 'default';
  };

  const LogDetailModal = ({ log }: { log: ScheduleJobLog }) => {
    let parsedApiResponse = null;
    try {
      parsedApiResponse = log.apiResponse ? JSON.parse(log.apiResponse) : null;
    } catch (e) {
      // Keep as string if not valid JSON
    }

    return (
      <div className="space-y-6">
        {/* Basic Info */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-semibold text-sm text-muted-foreground">BASIC INFORMATION</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Log ID:</span>
                <span className="text-sm font-mono">#{log.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">IMEI:</span>
                <span className="text-sm font-mono">{log.imei}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Action:</span>
                <div className="text-right">
                  <Badge variant={getActionBadgeVariant(log.action)}>
                    {log.action}
                  </Badge>
                  <div className="text-xs text-muted-foreground mt-1">
                    {log.action === 'engineStop' ? '(Immobilizer ON)' : '(Immobilizer OFF)'}
                  </div>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Trigger:</span>
                <Badge variant={log.trigger === 'user' ? 'secondary' : 'outline'}>
                  {log.trigger === 'user' ? 'User' : 'API'}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Success:</span>
                <div className="flex items-center gap-2">
                  {getSuccessIcon(log.success)}
                  <Badge variant={getSuccessBadgeVariant(log.success)}>
                    {getSuccessText(log.success)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold text-sm text-muted-foreground">TIMING</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Executed At:</span>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span className="text-sm">
                    {new Date(log.executedAt).toLocaleString()}
                  </span>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Created:</span>
                <span className="text-sm">{new Date(log.createdAt).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Updated:</span>
                <span className="text-sm">{new Date(log.updatedAt).toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Task Information */}
        <div className="space-y-2">
          <h4 className="font-semibold text-sm text-muted-foreground">TASK INFORMATION</h4>
          <div className="flex justify-between">
            <span className="text-sm">Task ID:</span>
            <span className="text-xs font-mono bg-muted px-2 py-1 rounded break-all">
              {log.taskId || 'N/A'}
            </span>
          </div>
          {/* Task Status Data */}
          <div className="mt-3">
            <div className="flex justify-between items-start">
              <span className="text-sm">Task Status:</span>
              <div className="text-right">
                {log.taskData ? (
                  <div className="space-y-1">
                    <div className="text-xs">
                      Status: <span className={`font-mono px-1 py-0.5 rounded text-xs ${
                        log.taskData?.data?.status === 'success'
                          ? 'bg-green-100 text-green-800'
                          : log.taskData?.data?.status === 'failed'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-muted'
                      }`}>
                        {log.taskData?.data?.status || 'Unknown'}
                      </span>
                    </div>
                    {log.taskData?.data?.message && (
                      <div className="text-xs text-muted-foreground">
                        {log.taskData.data.message}
                      </div>
                    )}
                  </div>
                ) : (
                  <span className="text-xs text-muted-foreground">No task status data</span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {log.errorMessage && (
          <div className="space-y-2">
            <h4 className="font-semibold text-sm text-muted-foreground">ERROR DETAILS</h4>
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-red-800">{log.errorMessage}</span>
              </div>
            </div>
          </div>
        )}

        {/* Task Status Data - Always show */}
        <div className="space-y-2">
          <h4 className="font-semibold text-sm text-muted-foreground">TASK STATUS DATA</h4>
          <div className="bg-muted rounded-md p-3">
            {log.taskData ? (
              <pre className="text-xs overflow-auto max-h-40">
                {JSON.stringify(log.taskData, null, 2)}
              </pre>
            ) : (
              <div className="text-xs text-muted-foreground italic">
                No task status data available
              </div>
            )}
          </div>
        </div>

        {/* API Response - Always show */}
        <div className="space-y-2">
          <h4 className="font-semibold text-sm text-muted-foreground">API RESPONSE</h4>
          <div className="bg-muted rounded-md p-3">
            {log.apiResponse ? (
              <pre className="text-xs overflow-auto max-h-40">
                {parsedApiResponse
                  ? JSON.stringify(parsedApiResponse, null, 2)
                  : log.apiResponse
                }
              </pre>
            ) : (
              <div className="text-xs text-muted-foreground italic">
                No API response data available
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Schedule Job Logs</h1>
          <p className="text-muted-foreground">
            View execution logs for scheduled immobilizer jobs ({pagination.totalCount} total records)
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={isRefreshing}
          variant="outline"
        >
          {isRefreshing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Log Data</CardTitle>
        </CardHeader>
        <CardContent>
          {logs.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Clock className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No schedule job logs found</h3>
              <p className="text-muted-foreground text-center">
                No scheduled jobs have been executed yet. Logs will appear here once scheduler starts triggering scheduled commands.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[80px]">ID</TableHead>
                      <TableHead className="min-w-[140px]">IMEI</TableHead>
                      <TableHead className="w-[100px]">Action</TableHead>
                      <TableHead className="w-[80px]">Trigger</TableHead>
                      <TableHead className="w-[100px]">Status</TableHead>
                      <TableHead className="w-[180px]">Executed At</TableHead>
                      <TableHead className="w-[140px]">Task ID</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {logs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell className="font-medium">#{log.id}</TableCell>
                        <TableCell className="font-mono text-sm">{log.imei}</TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <Badge variant={getActionBadgeVariant(log.action)}>
                              {log.action}
                            </Badge>
                            <div className="text-xs text-muted-foreground">
                              {log.action === 'engineStop' ? '(Immobilizer ON)' : '(Immobilizer OFF)'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={log.trigger === 'user' ? 'secondary' : 'outline'}>
                            {log.trigger === 'user' ? 'User' : 'API'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getSuccessIcon(log.success)}
                            <Badge variant={getSuccessBadgeVariant(log.success)}>
                              {getSuccessText(log.success)}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell className="text-sm">
                          {new Date(log.executedAt).toLocaleString()}
                        </TableCell>
                        <TableCell className="font-mono text-xs">
                          {log.taskId ? (
                            <span className="bg-muted px-2 py-1 rounded">
                              {log.taskId.substring(0, 8)}...
                            </span>
                          ) : (
                            <span className="text-muted-foreground">N/A</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedLog(log)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle className="flex items-center gap-2">
                                  <Clock className="h-5 w-5" />
                                  Schedule Job Log #{log.id}
                                </DialogTitle>
                                <DialogDescription>
                                  Detailed information for scheduled job execution
                                </DialogDescription>
                              </DialogHeader>
                              <LogDetailModal log={log} />
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination Controls */}
              <DataTablePagination
                pagination={pagination}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                isLoading={isLoading}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
