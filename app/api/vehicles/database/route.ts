import { NextRequest, NextResponse } from 'next/server';
import { getAllDevices } from '@/lib/db/queries';

export async function GET(request: NextRequest) {
  try {
    // Get all devices from database
    const devices = await getAllDevices();
    
    // Return list of IMEIs that exist in database
    const imeiList = devices.map(device => device.imei);
    
    return NextResponse.json({
      success: true,
      data: imeiList,
    });

  } catch (error) {
    console.error('Error fetching database devices:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch database devices',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
