import { NextRequest, NextResponse } from 'next/server';
import { getDeviceByImei, upsertDeviceUserStatus, createScheduleJobLog, getAdminConfig, updateScheduleJobLogTaskData } from '@/lib/db/queries';
import { db, deviceScheduleJobLogs } from '@/lib/db';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { imei } = body;

    if (!imei) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device IMEI is required',
        },
        { status: 400 }
      );
    }

    // Get device from database
    const device = await getDeviceByImei(imei);
    
    if (!device) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device not found',
        },
        { status: 404 }
      );
    }

    // Get the user_status to determine what action to take
    const userStatus = device.userStatus;
    const action = userStatus; // Use the user_status as the action to send to API

    // Get admin credentials for authentication
    const adminConfig = await getAdminConfig();
    
    if (!adminConfig) {
      const errorMessage = 'Admin configuration not found. Please configure admin credentials first.';
      
      // Log the failed attempt
      await createScheduleJobLog({
        imei,
        action: action as 'engineStop' | 'engineResume',
        trigger: 'api',
        success: 'false',
        errorMessage,
        executedAt: new Date(),
      });

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
        },
        { status: 400 }
      );
    }

    // First, authenticate to get token
    const loginResponse = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/login',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: adminConfig.username,
          password: adminConfig.password,
        }),
        cache: 'no-store',
      }
    );

    if (!loginResponse.ok) {
      const loginErrorText = await loginResponse.text();
      const errorMessage = `Authentication failed with status: ${loginResponse.status}. Response: ${loginErrorText}`;

      console.error('Login API error:', {
        status: loginResponse.status,
        response: loginErrorText,
        credentials: { username: adminConfig.username, password: '***' }
      });

      // Log the failed attempt
      await createScheduleJobLog({
        imei,
        action: action as 'engineStop' | 'engineResume',
        trigger: 'api',
        success: 'false',
        errorMessage,
        executedAt: new Date(),
      });

      return NextResponse.json(
        {
          success: false,
          error: 'Authentication failed',
          message: errorMessage,
        },
        { status: 401 }
      );
    }

    const loginData = await loginResponse.json();

    console.log('Login response data:', loginData); // Debug log

    if (!loginData.token) {
      const errorMessage = `No token received from authentication. Login response: ${JSON.stringify(loginData)}`;

      // Log the failed attempt
      await createScheduleJobLog({
        imei,
        action: action as 'engineStop' | 'engineResume',
        trigger: 'api',
        success: 'false',
        errorMessage,
        executedAt: new Date(),
      });

      return NextResponse.json(
        {
          success: false,
          error: 'Authentication failed',
          message: errorMessage,
        },
        { status: 401 }
      );
    }

    console.log('Using token for immobilizer API:', `Bearer ${loginData.token}`); // Debug log

    // Now make the immobilizer API call
    // Try with Bearer prefix first
    let immobilizerResponse = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/set_owl_mode_v1',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${loginData.token}`,
        },
        body: JSON.stringify({
          device_imei: imei,
          type: action,
        }),
        cache: 'no-store',
      }
    );

    // If Bearer format fails with 422, try without Bearer prefix
    if (!immobilizerResponse.ok && immobilizerResponse.status === 422) {
      console.log('Bearer format failed, trying without Bearer prefix...');
      immobilizerResponse = await fetch(
        'https://prod-s2.track360.net.in/api/v1/auth/set_owl_mode_v1',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': loginData.token,
          },
          body: JSON.stringify({
            device_imei: imei,
            type: action,
          }),
          cache: 'no-store',
        }
      );
    }

    const responseText = await immobilizerResponse.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      responseData = { raw_response: responseText };
    }

    if (!immobilizerResponse.ok) {
      const errorMessage = `Immobilizer API failed with status: ${immobilizerResponse.status}. Response: ${JSON.stringify(responseData)}`;

      console.error('Immobilizer API error details (after retry):', {
        status: immobilizerResponse.status,
        headers: Object.fromEntries(immobilizerResponse.headers.entries()),
        response: responseData,
        requestBody: { device_imei: imei, type: action },
        authHeaderTried: [`Bearer ${loginData.token}`, loginData.token]
      });

      // Log the failed attempt
      await createScheduleJobLog({
        imei,
        action: action as 'engineStop' | 'engineResume',
        trigger: 'api',
        success: 'false',
        errorMessage,
        apiResponse: JSON.stringify(responseData),
        executedAt: new Date(),
      });

      return NextResponse.json(
        {
          success: false,
          error: 'Immobilizer API failed',
          message: errorMessage,
          response: responseData,
        },
        { status: immobilizerResponse.status }
      );
    }

    // Extract task_id from response
    const taskId = responseData.task_id;

    // Update device with latest task_id (but keep user_status unchanged)
    if (taskId) {
      await upsertDeviceUserStatus(imei, action as 'engineStop' | 'engineResume', taskId);
    }

    // Log the pending attempt (will be updated after task status check)
    const logEntry = await createScheduleJobLog({
      imei,
      action: action as 'engineStop' | 'engineResume',
      trigger: 'api',
      taskId: taskId,
      apiResponse: JSON.stringify(responseData),
      success: 'pending',
      executedAt: new Date(),
    });

    // If we have a task_id, also check the task status
    if (taskId && logEntry) {
      try {
        console.log('Checking task status for task_id:', taskId);

        // Wait a bit for the device to process the command
        await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

        // Get admin credentials for authentication
        const adminConfig = await getAdminConfig();

        if (!adminConfig) {
          console.error('Admin configuration not found for task status check');
        } else {
          // Authenticate first
          const authResponse = await fetch('https://prod-s2.track360.net.in/api/v1/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              username: adminConfig.username,
              password: adminConfig.password,
            }),
            cache: 'no-store',
          });

          if (authResponse.ok) {
            const authData = await authResponse.json();
            const token = authData.token;

            // Check task status
            const taskStatusResponse = await fetch(
              `https://prod-s2.track360.net.in/api/v1/auth/get_task_status?task_id=${taskId}`,
              {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${token}`,
                },
                cache: 'no-store',
              }
            );

            if (taskStatusResponse.ok) {
              const taskStatusResult = await taskStatusResponse.json();

              // Determine if the task was actually successful based on the response
              let taskSuccess = 'true';
              let taskErrorMessage = null;

              if (taskStatusResult.data) {
                // Check if status is "failed" in the data
                if (taskStatusResult.data.status === 'failed') {
                  taskSuccess = 'false';
                  taskErrorMessage = taskStatusResult.data.message || 'Task failed';
                  console.log('Task failed:', taskStatusResult.data.message);
                } else if (taskStatusResult.data.status === 'success') {
                  console.log('Task successful:', taskStatusResult.data.message);
                }
              }

              // Update the log entry with task data
              await updateScheduleJobLogTaskData(logEntry.id, taskStatusResult);

              // Update the success status based on task result
              console.log('Updating schedule job log status:', {
                logId: logEntry.id,
                taskSuccess,
                taskErrorMessage,
                taskStatus: taskStatusResult.data?.status
              });

              const updateResult = await db
                .update(deviceScheduleJobLogs)
                .set({
                  success: taskSuccess,
                  errorMessage: taskErrorMessage,
                  updatedAt: new Date()
                })
                .where(eq(deviceScheduleJobLogs.id, logEntry.id))
                .returning();

              console.log('Schedule job log updated:', updateResult[0]);

              console.log('Task status check completed:', {
                taskId,
                status: taskStatusResult.data?.status,
                message: taskStatusResult.data?.message,
                success: taskSuccess,
                logUpdated: updateResult.length > 0
              });
            } else {
              const errorText = await taskStatusResponse.text();
              console.error('Task status API failed:', errorText);

              // Update log to indicate task status check failed
              await db
                .update(deviceScheduleJobLogs)
                .set({
                  success: 'false',
                  errorMessage: `Task status check failed: ${errorText}`,
                  updatedAt: new Date()
                })
                .where(eq(deviceScheduleJobLogs.id, logEntry.id));
            }
          } else {
            console.error('Authentication failed for task status check');

            // Update log to indicate authentication failed
            await db
              .update(deviceScheduleJobLogs)
              .set({
                success: 'false',
                errorMessage: 'Authentication failed for task status check',
                updatedAt: new Date()
              })
              .where(eq(deviceScheduleJobLogs.id, logEntry.id));
          }
        }
      } catch (taskStatusError) {
        console.error('Error checking task status:', taskStatusError);
        // Don't fail the main request if task status check fails
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        imei,
        action,
        userStatus,
        taskId,
        apiResponse: responseData,
      },
      message: `Scheduled immobilizer command executed successfully: ${action}`,
    });

  } catch (error) {
    console.error('Error in scheduled immobilizer job:', error);
    
    // Try to log the error if we have the imei
    const body = await request.json().catch(() => ({}));
    if (body.imei) {
      try {
        await createScheduleJobLog({
          imei: body.imei,
          action: 'engineResume', // Default action for error logging
          trigger: 'api',
          success: 'false',
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          executedAt: new Date(),
        });
      } catch (logError) {
        console.error('Failed to log error:', logError);
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Scheduled immobilizer job failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
