"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Save, UserCog } from 'lucide-react';

interface AdminConfig {
  id: number;
  username: string;
  password: string;
  latestSyncAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function AdminConfigPage() {
  const [adminConfig, setAdminConfig] = useState<AdminConfig | null>(null);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Load existing admin configuration
  useEffect(() => {
    loadAdminConfig();
  }, []);

  const loadAdminConfig = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin');
      const result = await response.json();

      if (result.success && result.data) {
        setAdminConfig(result.data);
        setUsername(result.data.username);
        setPassword(result.data.password);
      } else {
        // No admin config exists yet
        setAdminConfig(null);
        setUsername('');
        setPassword('');
      }
    } catch (error) {
      console.error('Error loading admin config:', error);
      toast({
        title: "Error",
        description: "Failed to load admin configuration",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!username.trim() || !password.trim()) {
      toast({
        title: "Validation Error",
        description: "Username and password are required",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);
      const response = await fetch('/api/admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: username.trim(),
          password: password.trim(),
        }),
      });

      const result = await response.json();

      if (result.success) {
        setAdminConfig(result.data);
        toast({
          title: "Success",
          description: result.message || "Admin configuration saved successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save admin configuration",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error saving admin config:', error);
      toast({
        title: "Error",
        description: "Failed to save admin configuration",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Configuration</h1>
        <p className="text-muted-foreground">
          Configure admin credentials for system access.
        </p>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCog className="h-5 w-5" />
            Admin Settings
          </CardTitle>
          <CardDescription>
            {adminConfig
              ? "Update the Track360 username and password for API access. Only one admin configuration is allowed."
              : "Set up the Track360 username and password for API access."
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Track360 Username</Label>
            <Input
              id="username"
              type="text"
              placeholder="your_track360_username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={isSaving}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Track360 Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="Enter Track360 password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSaving}
            />
          </div>

          {adminConfig && (
            <div className="pt-4 border-t">
              <div className="text-sm text-muted-foreground space-y-1">
                <p><strong>Created:</strong> {new Date(adminConfig.createdAt).toLocaleString()}</p>
                <p><strong>Last Updated:</strong> {new Date(adminConfig.updatedAt).toLocaleString()}</p>
                {adminConfig.latestSyncAt && (
                  <p><strong>Last Sync:</strong> {new Date(adminConfig.latestSyncAt).toLocaleString()}</p>
                )}
              </div>
            </div>
          )}

          <div className="flex justify-end pt-4">
            <Button 
              onClick={handleSave} 
              disabled={isSaving}
              className="min-w-[120px]"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Configuration
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
