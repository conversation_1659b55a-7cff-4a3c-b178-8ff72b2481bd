import { NextRequest, NextResponse } from 'next/server';
import { getDeviceByImei, getLatestImmobilizerLog, ensureDeviceExists } from '@/lib/db/queries';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ imei: string }> }
) {
  try {
    const { imei } = await params;
    const deviceImei = decodeURIComponent(imei);

    // Ensure device exists in database (create if not exists)
    const device = await ensureDeviceExists(deviceImei, 'engineResume');

    // Get latest log for additional context
    const latestLog = await getLatestImmobilizerLog(deviceImei);

    // Get current status from device (should always exist now)
    const userStatus = device.userStatus;
    const systemStatus = device.systemStatus;

    // Convert to boolean for frontend (based on user status)
    const isImmobilized = userStatus === 'engineStop';

    return NextResponse.json({
      success: true,
      data: {
        imei: deviceImei,
        userStatus,
        systemStatus,
        isImmobilized,
        device,
        latestLog,
        latestTaskId: device?.latestTaskId || null,
        lastUpdated: device?.updatedAt || latestLog?.createdAt || null,
      },
    });

  } catch (error) {
    console.error('Error fetching immobilizer status:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch immobilizer status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
